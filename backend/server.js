import express from 'express';
import cors from 'cors';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import bodyParser from 'body-parser';

// .env dosyasından ortam değişkenlerini yükle
dotenv.config();

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: '*', // Tüm kökenlere izin ver
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Veritabanı bağlantı ayarları
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'mehmet',
  password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
  database: process.env.DB_NAME || 'mgz24db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 10000, // 10 saniye
  // Aşağıdaki seçenekler MySQL2'de desteklenmiyor, kaldırıldı
  // enableKeepAlive: true,
  // keepAliveInitialDelay: 0,
  // idleTimeout: 60000,
  // socketTimeout: 60000
};

// Veritabanı bağlantı havuzu oluştur
const pool = mysql.createPool(dbConfig);

// Bağlantı hataları için olay dinleyicisi
pool.on('error', (err) => {
  console.error('Veritabanı havuzu hatası:', err);
  if (err.code === 'PROTOCOL_CONNECTION_LOST' || err.code === 'ECONNRESET') {
    console.log('Bağlantı yeniden kurulmaya çalışılıyor...');
  }
});

// Yardımcı fonksiyon: Sorgu çalıştırma (yeniden deneme mantığı ile)
const executeQuery = async (query, params = [], maxRetries = 3) => {
  let attempts = 0;

  while (attempts < maxRetries) {
    try {
      const connection = await pool.getConnection();
      try {
        const result = await connection.query(query, params);
        return result;
      } finally {
        connection.release();
      }
    } catch (error) {
      attempts++;
      console.error(`Sorgu hatası (Deneme ${attempts}/${maxRetries}):`, error);

      if (error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') {
        // Bağlantı hatası durumunda kısa bir bekleme
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else if (attempts >= maxRetries) {
        throw error; // Maksimum deneme sayısına ulaşıldı, hatayı ilet
      } else {
        throw error; // Diğer hataları hemen ilet
      }
    }
  }
};

// Bağlantıyı test et
app.get('/api/test-connection', async (req, res) => {
  try {
    // Basit bir sorgu ile bağlantıyı test et
    await executeQuery('SELECT 1 AS test');
    res.json({ success: true, message: 'Veritabanı bağlantısı başarılı' });
  } catch (error) {
    console.error('Veritabanı bağlantı hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Veritabanı bağlantı hatası',
      error: error.message,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// ====== API Rotaları ======

// Sevkiyat Rotaları
app.get('/api/sevkiyatlar', async (req, res) => {
  try {
    const { musteri_ID } = req.query;
    let query = `
            SELECT s.*, 
                l1.adi AS cikis_lokasyon_adi, 
                l2.adi AS varis_lokasyon_adi,
                t.adi AS nakliyeci_adi,
                u.adi AS urun_adi
            FROM sevkiyatlar s
            LEFT JOIN lokasyonlar l1 ON s.cikis_lokasyon_id = l1.id
            LEFT JOIN lokasyonlar l2 ON s.varis_lokasyon_id = l2.id
            LEFT JOIN tasiyicilar t ON s.nakliyeci_id = t.id
            LEFT JOIN urunler u ON s.urun_id = u.id`;
    const params = [];

    // Eğer müşteri ID'si belirtilmişse, sadece o müşteriye ait sevkiyatları getir
    if (musteri_ID) {
      query += ' WHERE s.musteri_ID = ?';
      params.push(musteri_ID);
    }

    // Sonuçları oluşturma zamanına göre azalan sırayla getir (en yeniden en eskiye)
    query += ' ORDER BY s.olusturma_zamani DESC';

    const [rows] = await executeQuery(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Sevkiyatlar alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/sevkiyatlar/:id', async (req, res) => {
  try {
    const [rows] = await executeQuery(`
            SELECT s.*, 
                l1.adi AS cikis_lokasyon_adi, 
                l2.adi AS varis_lokasyon_adi,
                t.adi AS nakliyeci_adi,
                u.adi AS urun_adi
            FROM sevkiyatlar s
            LEFT JOIN lokasyonlar l1 ON s.cikis_lokasyon_id = l1.id
            LEFT JOIN lokasyonlar l2 ON s.varis_lokasyon_id = l2.id
            LEFT JOIN tasiyicilar t ON s.nakliyeci_id = t.id
            LEFT JOIN urunler u ON s.urun_id = u.id
            WHERE s.id = ?`, [req.params.id]);

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Sevkiyat bulunamadı' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Sevkiyat alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/sevkiyatlar', async (req, res) => {
  try {
    const {
      mgz24_id, sevkiyat_ad, plaka_no, nereden, nereye, nakliyeci,
      sofor_gsm, urun, palet, net, brut, sicaklik_aralik, musteri_ID
    } = req.body;

    // Eğer musteri_ID belirtilmezse, hata döndür
    if (!musteri_ID) {
      return res.status(400).json({ error: 'Müşteri ID gereklidir' });
    }

    // Lokasyon ID'lerini isme göre alın
    let cikisLokasyonId = null;
    let varisLokasyonId = null;
    let nakliyeciId = null;
    let urunId = null;

    // Çıkış lokasyonu ID'sini bul
    if (nereden) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(nereden) && Number.isInteger(Number(nereden))) {
        cikisLokasyonId = parseInt(nereden);
      } else {
        const [cikisLokasyonlar] = await executeQuery('SELECT id FROM lokasyonlar WHERE adi = ?', [nereden]);
        if (cikisLokasyonlar.length > 0) {
          cikisLokasyonId = cikisLokasyonlar[0].id;
        } else {
          // Lokasyon bulunamazsa hata döndür
          return res.status(400).json({ error: `Çıkış lokasyonu "${nereden}" bulunamadı` });
        }
      }
    }

    // Varış lokasyonu ID'sini bul
    if (nereye) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(nereye) && Number.isInteger(Number(nereye))) {
        varisLokasyonId = parseInt(nereye);
      } else {
        const [varisLokasyonlar] = await executeQuery('SELECT id FROM lokasyonlar WHERE adi = ?', [nereye]);
        if (varisLokasyonlar.length > 0) {
          varisLokasyonId = varisLokasyonlar[0].id;
        } else {
          // Lokasyon bulunamazsa hata döndür
          return res.status(400).json({ error: `Varış lokasyonu "${nereye}" bulunamadı` });
        }
      }
    }

    // Nakliyeci ID'sini bul
    if (nakliyeci) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(nakliyeci) && Number.isInteger(Number(nakliyeci))) {
        nakliyeciId = parseInt(nakliyeci);
      } else {
        const [nakliyeciler] = await executeQuery('SELECT id FROM tasiyicilar WHERE adi = ?', [nakliyeci]);
        if (nakliyeciler.length > 0) {
          nakliyeciId = nakliyeciler[0].id;
        } else {
          // Nakliyeci bulunamazsa hata döndür
          return res.status(400).json({ error: `Nakliyeci "${nakliyeci}" bulunamadı` });
        }
      }
    }

    // Ürün ID'sini bul
    if (urun) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(urun) && Number.isInteger(Number(urun))) {
        urunId = parseInt(urun);
      } else {
        const [urunler] = await executeQuery('SELECT id FROM urunler WHERE adi = ?', [urun]);
        if (urunler.length > 0) {
          urunId = urunler[0].id;
        } else {
          // Ürün bulunamazsa hata döndür
          return res.status(400).json({ error: `Ürün "${urun}" bulunamadı` });
        }
      }
    }

    // Sevkiyat ID'si oluştur (örnek: SV-202408001)
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');

    // Aynı ay içindeki sevkiyat sayısını kontrol et ve sıra numarası oluştur
    const [countResult] = await executeQuery(
      'SELECT COUNT(*) as count FROM sevkiyatlar WHERE YEAR(olusturma_zamani) = ? AND MONTH(olusturma_zamani) = ?',
      [year, date.getMonth() + 1]
    );
    const count = countResult[0].count + 1;
    const sequenceNumber = String(count).padStart(3, '0');

    const sevkiyatID = `SV-${year}${month}${sequenceNumber}`;

    const [result] = await executeQuery(
      `INSERT INTO sevkiyatlar (
                sevkiyat_ID, mgz24_kodu, sevkiyat_adi, plaka_no, cikis_lokasyon_id, varis_lokasyon_id, 
                nakliyeci_id, surucu_telefon, urun_id, palet_sayisi, net_agirlik, brut_agirlik, 
                sicaklik_araligi, durum, musteri_ID
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'hazırlanıyor', ?)`,
      [
        sevkiyatID, mgz24_id, sevkiyat_ad, plaka_no, cikisLokasyonId, varisLokasyonId, nakliyeciId,
        sofor_gsm, urunId, palet, net, brut, sicaklik_aralik, musteri_ID
      ]
    );

    res.status(201).json({
      id: result.insertId,
      sevkiyat_ID: sevkiyatID,
      message: 'Sevkiyat başarıyla oluşturuldu'
    });
  } catch (error) {
    console.error('Sevkiyat eklenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/sevkiyatlar/:id', async (req, res) => {
  try {
    const {
      mgz24_id, sevkiyat_ad, plaka_no, nereden, nereye, nakliyeci,
      sofor_gsm, urun, palet, net, brut, sicaklik_aralik, durum
    } = req.body;

    // Lokasyon ID'lerini isme göre alın
    let cikisLokasyonId = null;
    let varisLokasyonId = null;
    let nakliyeciId = null;
    let urunId = null;

    // Çıkış lokasyonu ID'sini bul
    if (nereden) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(nereden) && Number.isInteger(Number(nereden))) {
        cikisLokasyonId = parseInt(nereden);
      } else {
        const [cikisLokasyonlar] = await executeQuery('SELECT id FROM lokasyonlar WHERE adi = ?', [nereden]);
        if (cikisLokasyonlar.length > 0) {
          cikisLokasyonId = cikisLokasyonlar[0].id;
        } else {
          // Lokasyon bulunamazsa hata döndür
          return res.status(400).json({ error: `Çıkış lokasyonu "${nereden}" bulunamadı` });
        }
      }
    }

    // Varış lokasyonu ID'sini bul
    if (nereye) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(nereye) && Number.isInteger(Number(nereye))) {
        varisLokasyonId = parseInt(nereye);
      } else {
        const [varisLokasyonlar] = await executeQuery('SELECT id FROM lokasyonlar WHERE adi = ?', [nereye]);
        if (varisLokasyonlar.length > 0) {
          varisLokasyonId = varisLokasyonlar[0].id;
        } else {
          // Lokasyon bulunamazsa hata döndür
          return res.status(400).json({ error: `Varış lokasyonu "${nereye}" bulunamadı` });
        }
      }
    }

    // Nakliyeci ID'sini bul
    if (nakliyeci) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(nakliyeci) && Number.isInteger(Number(nakliyeci))) {
        nakliyeciId = parseInt(nakliyeci);
      } else {
        const [nakliyeciler] = await executeQuery('SELECT id FROM tasiyicilar WHERE adi = ?', [nakliyeci]);
        if (nakliyeciler.length > 0) {
          nakliyeciId = nakliyeciler[0].id;
        } else {
          // Nakliyeci bulunamazsa hata döndür
          return res.status(400).json({ error: `Nakliyeci "${nakliyeci}" bulunamadı` });
        }
      }
    }

    // Ürün ID'sini bul
    if (urun) {
      // ID gönderildiyse doğrudan kullan, değilse isimle sorgula
      if (!isNaN(urun) && Number.isInteger(Number(urun))) {
        urunId = parseInt(urun);
      } else {
        const [urunler] = await executeQuery('SELECT id FROM urunler WHERE adi = ?', [urun]);
        if (urunler.length > 0) {
          urunId = urunler[0].id;
        } else {
          // Ürün bulunamazsa hata döndür
          return res.status(400).json({ error: `Ürün "${urun}" bulunamadı` });
        }
      }
    }

    const [result] = await executeQuery(
      `UPDATE sevkiyatlar SET 
                mgz24_kodu = ?, sevkiyat_adi = ?, plaka_no = ?, cikis_lokasyon_id = ?, 
                varis_lokasyon_id = ?, nakliyeci_id = ?, surucu_telefon = ?, urun_id = ?, 
                palet_sayisi = ?, net_agirlik = ?, brut_agirlik = ?, sicaklik_araligi = ?,
                durum = ?
            WHERE id = ?`,
      [
        mgz24_id, sevkiyat_ad, plaka_no, cikisLokasyonId, varisLokasyonId, nakliyeciId,
        sofor_gsm, urunId, palet, net, brut, sicaklik_aralik, durum, req.params.id
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Sevkiyat bulunamadı' });
    }

    res.json({ message: 'Sevkiyat başarıyla güncellendi' });
  } catch (error) {
    console.error('Sevkiyat güncellenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Kullanıcı Rotaları
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login isteği alındı:', req.body);
    const { email, password } = req.body;

    // E-posta ve şifre kontrolü
    if (!email || !password) {
      console.log('Eksik kimlik bilgileri:', { email, password: password ? '****' : undefined });
      return res.status(400).json({ message: 'E-posta ve şifre gereklidir' });
    }

    // Kullanıcıyı e-posta adresine göre bul
    const [users] = await executeQuery(
      `SELECT musteri_ID, kullanici, email, password, musteri_adi, tel, firma, adres, gorev, 
             olusturma_tarihi, guncelleme_tarihi 
             FROM kullanicilar 
             WHERE email = ?`,
      [email]
    );

    console.log('Kullanıcı sorgusu sonucu:', users.length > 0 ? 'Kullanıcı bulundu' : 'Kullanıcı bulunamadı');

    if (users.length === 0) {
      return res.status(401).json({ message: 'Geçersiz e-posta veya şifre' });
    }

    const user = users[0];

    // Gerçek uygulamada şifre karşılaştırması yapılmalıdır
    // Örneğin: bcrypt.compareSync(password, user.password)
    // Burada basitlik için doğrudan karşılaştırıyoruz
    console.log('Şifre karşılaştırılıyor:', password === user.password ? 'Eşleşti' : 'Eşleşmedi');

    if (password !== user.password) {
      return res.status(401).json({ message: 'Geçersiz e-posta veya şifre' });
    }

    // Hassas bilgileri kaldır
    delete user.password;

    console.log('Başarılı giriş, kullanıcı:', user.kullanici);

    res.json({
      user,
      token: 'dummy-jwt-token' // Gerçek uygulamada JWT token oluşturulmalıdır
    });
  } catch (error) {
    console.error('Giriş yapılırken hata:', error);
    res.status(500).json({
      error: error.message,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// Nakliyeci Rotaları
app.get('/api/nakliyeciler', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM tasiyicilar');
    res.json(rows);
  } catch (error) {
    console.error('Nakliyeciler alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Ürün Rotaları
app.get('/api/urunler', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM urunler');
    res.json(rows);
  } catch (error) {
    console.error('Ürünler alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Lokasyon Rotaları
app.get('/api/lokasyonlar', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM lokasyonlar');
    res.json(rows);
  } catch (error) {
    console.error('Lokasyonlar alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Sensör Veri Rotaları
app.get('/api/sensor-data/sevkiyat/:sevkiyatId', async (req, res) => {
  try {
    const [rows] = await executeQuery(
      `SELECT * FROM cihazBilgi 
             WHERE sevkiyat_id = ? 
             ORDER BY zaman DESC`,
      [req.params.sevkiyatId]
    );
    res.json(rows);
  } catch (error) {
    console.error('Sensör verileri alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Sıcaklık alarm rotası
app.get('/api/alarms/temperature/:sevkiyatId', async (req, res) => {
  try {
    const [shipment] = await executeQuery(
      'SELECT sicaklik_araligi FROM sevkiyatlar WHERE id = ?',
      [req.params.sevkiyatId]
    );

    if (shipment.length === 0) {
      return res.status(404).json({ message: 'Sevkiyat bulunamadı' });
    }

    // Sıcaklık aralığını parse et
    const tempRange = shipment[0].sicaklik_araligi;
    const matches = tempRange.match(/(-?\d+(?:\.\d+)?)°C\s*-\s*(-?\d+(?:\.\d+)?)°C/);

    if (!matches) {
      return res.status(400).json({ message: 'Geçersiz sıcaklık aralığı formatı' });
    }

    const minTemp = parseFloat(matches[1]);
    const maxTemp = parseFloat(matches[2]);

    const [alarms] = await executeQuery(
      `SELECT * FROM cihazBilgi
             WHERE sevkiyat_id = ? AND okuma_tipi = 'sicaklik' AND (sicaklik < ? OR sicaklik > ?)
             ORDER BY zaman DESC`,
      [req.params.sevkiyatId, minTemp, maxTemp]
    );

    res.json(alarms);
  } catch (error) {
    console.error('Sıcaklık alarmları alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Müşteri Rotaları
app.get('/api/musteriler', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM musteriler');
    res.json(rows);
  } catch (error) {
    console.error('Müşteriler alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/musteriler/:id', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM musteriler WHERE musteri_id = ?', [req.params.id]);
    if (rows.length === 0) {
      return res.status(404).json({ message: 'Müşteri bulunamadı' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Müşteri alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/musteriler', async (req, res) => {
  try {
    const {
      ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
    } = req.body;

    const [result] = await executeQuery(
      `INSERT INTO musteriler (
                ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
      ]
    );

    res.status(201).json({
      id: result.insertId,
      message: 'Müşteri başarıyla oluşturuldu'
    });
  } catch (error) {
    console.error('Müşteri eklenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/musteriler/:id', async (req, res) => {
  try {
    const {
      ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
    } = req.body;

    const [result] = await executeQuery(
      `UPDATE musteriler SET 
                ad = ?, soyad = ?, e_posta = ?, telefon = ?, sifre = ?, 
                kullanici_seviyesi = ?, firma_ad = ?, firma_adres = ?, kategori = ?
            WHERE musteri_id = ?`,
      [
        ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi,
        firma_ad, firma_adres, kategori, req.params.id
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Müşteri bulunamadı' });
    }

    res.json({ message: 'Müşteri başarıyla güncellendi' });
  } catch (error) {
    console.error('Müşteri güncellenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/musteriler/:id', async (req, res) => {
  try {
    const [result] = await executeQuery('DELETE FROM musteriler WHERE musteri_id = ?', [req.params.id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Müşteri bulunamadı' });
    }

    res.json({ message: 'Müşteri başarıyla silindi' });
  } catch (error) {
    console.error('Müşteri silinirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Kullanıcılar (kullanicilar) tablosu için endpointler

// Tüm kullanıcıları getir (sadece admin rolüne sahip kullanıcılar için)
app.get('/api/kullanicilar', async (req, res) => {
  try {
    const [users] = await executeQuery(
      `SELECT musteri_ID, kullanici, email, musteri_adi, tel, firma, adres, gorev, 
             olusturma_tarihi, guncelleme_tarihi 
             FROM kullanicilar`
    );
    res.json(users);
  } catch (error) {
    console.error('Kullanıcılar getirilirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// ID'ye göre kullanıcı getir
app.get('/api/kullanicilar/:id', async (req, res) => {
  try {
    const [users] = await executeQuery(
      `SELECT musteri_ID, kullanici, email, musteri_adi, tel, firma, adres, gorev, 
             olusturma_tarihi, guncelleme_tarihi 
             FROM kullanicilar 
             WHERE musteri_ID = ?`,
      [req.params.id]
    );

    if (users.length === 0) {
      return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
    }

    res.json(users[0]);
  } catch (error) {
    console.error('Kullanıcı bilgileri getirilirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Kullanıcı güncelle
app.put('/api/kullanicilar/:id', async (req, res) => {
  try {
    const {
      kullanici, email, musteri_adi, tel, firma, adres,
      current_password, password
    } = req.body;

    // Şifre değişikliği varsa
    if (current_password && password) {
      // Önce mevcut şifre kontrolü yap
      const [users] = await executeQuery(
        'SELECT password FROM kullanicilar WHERE musteri_ID = ?',
        [req.params.id]
      );

      if (users.length === 0) {
        return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
      }

      const storedPassword = users[0].password;

      // Burada şifre doğrulama işlemi yapılabilir (bcrypt vb.)
      // Örnek olarak doğrudan karşılaştırma kullanıyoruz
      if (storedPassword !== current_password) {
        return res.status(401).json({ message: 'Mevcut şifre yanlış' });
      }

      // Şifre güncellemesi yap
      const [result] = await executeQuery(
        `UPDATE kullanicilar SET 
                    kullanici = ?, email = ?, musteri_adi = ?, tel = ?, 
                    firma = ?, adres = ?, password = ?, guncelleme_tarihi = NOW()
                WHERE musteri_ID = ?`,
        [kullanici, email, musteri_adi, tel, firma, adres, password, req.params.id]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
      }
    } else {
      // Şifre değişikliği yoksa sadece diğer bilgileri güncelle
      const [result] = await executeQuery(
        `UPDATE kullanicilar SET 
                    kullanici = ?, email = ?, musteri_adi = ?, tel = ?, 
                    firma = ?, adres = ?, guncelleme_tarihi = NOW()
                WHERE musteri_ID = ?`,
        [kullanici, email, musteri_adi, tel, firma, adres, req.params.id]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
      }
    }

    res.json({ message: 'Kullanıcı başarıyla güncellendi' });
  } catch (error) {
    console.error('Kullanıcı güncellenirken hata:', error);

    // Benzersizlik kısıtlaması hatası
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({
        message: 'Bu e-posta adresi veya kullanıcı adı başka bir kullanıcı tarafından kullanılıyor.'
      });
    }

    res.status(500).json({ error: error.message });
  }
});

// Ödeme ve Paket Rotaları
app.get('/api/payments/packages', async (req, res) => {
  try {
    // Burada veritabanından paketler çekilebilir
    // Şimdilik sabit veri döndürüyoruz
    const packages = [
      { id: 1, days: 5, priceEUR: 3, name: '5 Gün Kullanım' },
      { id: 2, days: 10, priceEUR: 5, name: '10 Gün Kullanım' },
      { id: 3, days: 20, priceEUR: 6, name: '20 Gün Kullanım' },
      { id: 4, days: 30, priceEUR: 7, name: '30 Gün Kullanım' },
      { id: 5, days: 90, priceEUR: 18, name: '3 Ay Kullanım' },
      { id: 6, days: 180, priceEUR: 33, name: '6 Ay Kullanım' },
      { id: 7, days: 360, priceEUR: 60, name: '1 Yıl Kullanım' }
    ];

    res.json(packages);
  } catch (error) {
    console.error('Paketler alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/payments/exchange-rate', async (req, res) => {
  try {
    // Burada gerçek bir API'den döviz kuru alınabilir
    // Şimdilik sabit değer döndürüyoruz
    const exchangeRate = 34.98; // Euro/TL kuru

    res.json({
      EUR: {
        selling: exchangeRate,
        buying: exchangeRate - 0.10,
        updated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Döviz kuru alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/payments/history/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;

    // Veritabanından kullanıcının ödeme geçmişini çek
    const [rows] = await executeQuery(
      `SELECT * FROM kullanici_odemeleri WHERE musteri_ID = ? ORDER BY odeme_tarihi DESC`,
      [userId]
    );

    // Sonuçları düzenle ve gönder
    const formattedPayments = rows.map(payment => ({
      id: payment.islem_id,
      packageName: payment.paket_adi,
      amountEUR: parseFloat(payment.tutar_eur),
      amountTRY: parseFloat(payment.tutar_tl),
      exchangeRate: parseFloat(payment.doviz_kuru),
      date: payment.odeme_tarihi,
      status: payment.durum
    }));

    res.json(formattedPayments);
  } catch (error) {
    console.error('Ödeme geçmişi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/users/:userId/usage', async (req, res) => {
  try {
    const userId = req.params.userId;

    // Veritabanından kullanıcının kullanım süresini çek
    const [rows] = await executeQuery(
      `SELECT * FROM kullanici_kullanim_sureleri WHERE musteri_ID = ?`,
      [userId]
    );

    if (rows.length === 0) {
      return res.json({
        remainingDays: 0,
        expiryDate: null,
        status: 'inactive'
      });
    }

    const usage = rows[0];
    const expiryDate = new Date(usage.bitis_tarihi);
    const today = new Date();

    // Kalan gün sayısını hesapla
    const diffTime = expiryDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    res.json({
      remainingDays: diffDays > 0 ? diffDays : 0,
      expiryDate: usage.bitis_tarihi,
      status: diffDays > 0 ? 'active' : 'expired'
    });
  } catch (error) {
    console.error('Kullanım süresi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/payments/process', async (req, res) => {
  try {
    const {
      userId,
      packageId,
      cardDetails,
      amount
    } = req.body;

    // Gerçek uygulamada burada ödeme işlemi gerçekleştirilir
    // Test için başarılı ödeme simüle ediyoruz

    // Yeni işlem ID'si oluştur
    const transactionId = 'TRX-' + Math.random().toString(36).substring(2, 10).toUpperCase();

    // Paketi bul
    const [packages] = await executeQuery('SELECT * FROM paketler WHERE id = ?', [packageId]);

    if (packages.length === 0) {
      return res.status(404).json({ success: false, message: 'Paket bulunamadı' });
    }

    const selectedPackage = packages[0];

    // Ödeme kaydını veritabanına ekle
    await executeQuery(
      `INSERT INTO kullanici_odemeleri (
        islem_id, musteri_ID, paket_id, paket_adi, tutar_eur, tutar_tl, 
        doviz_kuru, odeme_tarihi, durum, gun_sayisi
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), 'completed', ?)`,
      [
        transactionId,
        userId,
        packageId,
        selectedPackage.name,
        amount.eur,
        amount.try,
        amount.exchangeRate,
        selectedPackage.days
      ]
    );

    // Kullanıcının mevcut kullanım süresini kontrol et
    const [userUsage] = await executeQuery(
      'SELECT * FROM kullanici_kullanim_sureleri WHERE musteri_ID = ?',
      [userId]
    );

    const today = new Date();
    let newExpiryDate;

    if (userUsage.length > 0) {
      // Mevcut kayıt var, süreyi uzat
      const currentExpiry = new Date(userUsage[0].bitis_tarihi);

      // Eğer mevcut süre dolmuşsa bugünden başlat, dolmamışsa mevcut sürenin üzerine ekle
      if (currentExpiry < today) {
        newExpiryDate = new Date(today);
      } else {
        newExpiryDate = new Date(currentExpiry);
      }

      // Seçilen paket kadar gün ekle
      newExpiryDate.setDate(newExpiryDate.getDate() + selectedPackage.days);

      // Kaydı güncelle
      await executeQuery(
        `UPDATE kullanici_kullanim_sureleri 
        SET bitis_tarihi = ?, guncelleme_tarihi = NOW() 
        WHERE musteri_ID = ?`,
        [newExpiryDate, userId]
      );
    } else {
      // Yeni kayıt oluştur
      newExpiryDate = new Date(today);
      newExpiryDate.setDate(newExpiryDate.getDate() + selectedPackage.days);

      await executeQuery(
        `INSERT INTO kullanici_kullanim_sureleri (
          musteri_ID, baslangic_tarihi, bitis_tarihi, olusturma_tarihi, guncelleme_tarihi
        ) VALUES (?, NOW(), ?, NOW(), NOW())`,
        [userId, newExpiryDate]
      );
    }

    res.json({
      success: true,
      message: 'Ödeme başarıyla gerçekleştirildi',
      transactionId: transactionId,
      expiryDate: newExpiryDate,
      date: new Date()
    });
  } catch (error) {
    console.error('Ödeme işlemi sırasında hata:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Düzgün kapatma işlemleri
process.on('SIGINT', async () => {
  console.log('Sunucu kapatılıyor...');

  try {
    // Bağlantı havuzunu kapat
    await pool.end();
    console.log('Veritabanı bağlantısı kapatıldı');
  } catch (err) {
    console.error('Veritabanı havuzu kapatılırken hata:', err);
  }

  process.exit(0);
});

// Beklenmeyen hatalar için
process.on('uncaughtException', (err) => {
  console.error('Beklenmeyen hata:', err);
  // Kritik bir hata durumunda bile düzgün kapanma denemesi
  try {
    pool.end().then(() => {
      console.log('Veritabanı bağlantısı kapatıldı');
      process.exit(1);
    }).catch(() => {
      process.exit(1);
    });
  } catch (e) {
    process.exit(1);
  }
});

// Server'ı başlat
app.listen(port, '0.0.0.0', () => {
  console.log(`Server ${port} portunda çalışıyor - Tüm arayüzlerden erişilebilir`);
}); 