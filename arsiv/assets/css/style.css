/*palet-> pri: #007f97 | lighter: #2bb2c4 | darker: #0e3e63*/
body {
  font-family: 'Roboto', sans-serif;
  color: #777;
}

/*over*/
.bg-darkblue {
  background-color: #0e3e63;
}

main .h4, main .h5{
  color: #0e3e63 !important;
}
/*over*/

/*yan menu*/
@media (min-width: 768px) {
  .sidebar .offcanvas-lg {
    position: -webkit-sticky;
    position: sticky;
    top: 48px;
  }
  .sidebar {
    height: 100vh;
  }
  .navbar-search {
    display: block;
  }
  .navbar-brand img {
    height: 54px;
    opacity: .85;
  }  
}
@media (max-width: 768px) {
  .navbar-brand img {
    height: 27px;
  }
}

.sidebar .nav-link {
  font-size: 1rem;
  color: #414141;
}

.sidebar .nav-pills .nav-link.active, .sidebar .nav-pills .show>.nav-link {
  background-color: rgba(43, 178, 196, 0.35);
  color: #414141;
  border-radius: 0;
  /* color: #fff; */
}
.sidebar .nav-pills .nav-link:hover {
  color: #999;
}

.sidebar-heading {
font-size: .75rem;
}

.navbar-brand {
padding-top: .75rem;
padding-bottom: .75rem;
background-color: rgba(0, 0, 0, .25);
box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

.navbar .form-control {
padding: .75rem 1rem;
}

/*cesitli*/
.bg-yes {/*.bd-green-400*/
  color: #fff;
  background-color: #39956d !important;/*66ae8f*/
}
.bg-mav {/*.bd-blue-500*/
  color: #fff;
  background-color: #0060f7 !important;/**/
}
.bg-sar {/*.bd-yellow-500*/
  color: #fff;
  background-color: #ffba2c !important;/**/
}
.bg-kir {/*.bd-red-500*/
  color: #fff;
  background-color: #dc3645 !important;/*dc3545*/
}

.bg-kir-75 {
  color: #fff;
  background-color: rgba(220, 53, 69, .75) !important;
}
.bg-pem-75 {
  color: #fff;
  background-color: rgba(214, 51, 132, .75)!important;
}
.bg-yes-75 {
  color: #fff;
  background-color: rgba(25, 135, 84, .75) !important;
} 

.ince{
  --bs-border-opacity: .25;
}
.deger {
  font-size: 4em;
  font-weight: 100;
  font-family: 'Nixie One';
}

label {
  /* color: #fff !important; */
  background-color: #e1e1e1 !important;
  padding: .125rem .5rem;
  border-radius: .25rem;
  margin-bottom: .75rem;
}

.irs-bar, .irs--flat .irs-from, .irs--flat .irs-to, .irs--flat .irs-single, .irs--flat .irs-handle>i:first-child {
  background-color: #007f97 !important;
}

.irs--flat .irs-from:before, .irs--flat .irs-to:before, .irs--flat .irs-single:before {
  border-top-color: #007f97;
}

/*uti*/
.rel{
  position: relative;
}
.abs{
  position: absolute;
  top: 10px;
  right: 10px;
}
/*uti*/

/*yer tutucular -> production versiyona geçince sil*/



/*yer tutucular*/