gzcgold web tasarım prompt
Şöyle bir uygulama yapmak istiyorum. 

1- api endpointten CihazID ile alınan  aşağıdaki bilgiler var.
| Field       | Type         | Null | Key | Default | Extra          |
+-------------+--------------+------+-----+---------+----------------+
| ID          | int          | NO   | PRI | NULL    | auto_increment |
| IP_adres    | varchar(50)  | YES  |     | NULL    |                |
| Tarih       | datetime     | YES  | MUL | NULL    |                |
| CihazID     | varchar(50)  | YES  | MUL | NULL    |                |
| ICCID       | varchar(50)  | YES  |     | NULL    |                |
| Operator    | varchar(50)  | YES  |     | NULL    |                |
| Ulke        | varchar(50)  | YES  |     | NULL    |                |
| Enlem       | double(10,7) | YES  |     | NULL    |                |
| Boylam      | double(10,7) | YES  |     | NULL    |                |
| URL         | text         | NO   |     | NULL    |                |
| DataNo      | int          | YES  |     | NULL    |                |
| Sicaklik    | varchar(50)  | YES  |     | NULL    |                |
| Nem         | varchar(50)  | YES  |     | NULL    |                |
| Basinc      | varchar(50)  | YES  |     | NULL    |                |
| Isik        | varchar(50)  | YES  |     | NULL    |                |
| Pil         | varchar(50)  | YES  |     | NULL    |                |
| KayitAralik | varchar(50)  | YES  |     | NULL    |                |
| CihazGun    | varchar(50)  | YES  |     | NULL    |                |
| Secim       | tinyint(1)   | YES  |     | NULL    |                |
| Notlar      | varchar(255) | YES  |     | NULL    |                |
| Aktarim     | tinyint(1)   | YES  | MUL | 0       |                |
+-------------+--------------+------+-----+---------+-----

+--------------------+--------------+------+-----+---------+----------------+
| Field              | Type         | Null | Key | Default | Extra          |
+--------------------+--------------+------+-----+---------+----------------+
| musteri_id         | int          | NO   | PRI | NULL    | auto_increment |
| ad                 | varchar(50)  | NO   |     | NULL    |                |
| soyad              | varchar(50)  | NO   |     | NULL    |                |
| e_posta            | varchar(100) | NO   | UNI | NULL    |                |
| telefon            | varchar(20)  | YES  |     | NULL    |                |
| sifre              | varchar(255) | NO   |     | NULL    |                |
| kullanici_seviyesi | int          | NO   |     | NULL    |                |
| reset_key          | varchar(64)  | YES  |     | NULL    |                |
| firma_ad           | varchar(255) | YES  |     | NULL    |                |
| firma_adres        | varchar(255) | YES  |     | NULL    |                |
| kategori           | varchar(255) | YES  |     | NULL    |                |
+--------------------+--------------+------+-----+---------+----------------+
müşteri kaydı yapma, müşteriye cihaz tanımlama, müşterinin nakliye başlatması gibi işlemleri yapan
react web uygulaması yapmak istiyorum.bu bilgileri alıp m