<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Gelen tüm parametreleri logla
error_log("basarili.php'ye gelen parametreler: " . print_r($_POST, true));

// API bilgileri
$client_code = "58437";
$guid = "D8AE7D3C-31B4-453A-8E53-49F0A8B68927";
$client_user = '**********';
$client_pass = '0D004378D1733A78';
$wsdl_url = 'https://posws.param.com.tr/turkpos.ws/service_turkpos_prod.asmx?wsdl';

try {
    // SOAP istemcisi oluşturma
    $client = new SoapClient($wsdl_url, ['trace' => 1, 'exceptions' => 1]);
} catch (SoapFault $fault) {
    error_log("SOAP istemcisi oluşturulamadı: " . $fault->getMessage());
    die("Ödeme sistemi şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.");
}

// 3D doğrulama sonrası gelen parametreleri kontrol et
if (isset($_POST['md']) && isset($_POST['orderId'])) {
    $md = $_POST['md'];
    $mdStatus = $_POST['mdStatus'] ?? '';
    $orderId = $_POST['orderId'];
    $islemGUID = $_POST['islemGUID'] ?? '';

    // Islem_GUID'nin 36 karakter olup olmadığını kontrol et
    if (strlen($islemGUID) !== 36) {
        die("Islem_GUID parametresi 36 hane olmalıdır.");
    }

    // Hash oluştur
    $hashStr = $islemGUID . $md . $mdStatus . $orderId . strtolower($guid);
    $hash = base64_encode(sha1($hashStr, true));

    if (empty($hash)) {
        error_log("Hash oluşturulamadı. Hash string: " . $hashStr);
        die("İşlem güvenlik hatası oluştu. Lütfen daha sonra tekrar deneyin.");
    }

    $transactionAmount = $_POST['transactionAmount'] ?? '0.00';
    // Virgül yerine nokta kullanarak sayısal formata çevir
    $transactionAmount = str_replace(',', '.', $transactionAmount);

    try {
        // TP_WMD_Pay işlemi
        $payParams = array(
            'G' => array(
                'CLIENT_CODE' => $client_code,
                'CLIENT_USERNAME' => $client_user,
                'CLIENT_PASSWORD' => $client_pass,
            ),
            'GUID' => $guid,
            'UCD_MD' => $md,
            'Islem_GUID' => $islemGUID,
            'Siparis_ID' => $orderId,
        );

        // TP_WMD_Pay işlemi başlat
        $payResponse = $client->TP_WMD_Pay($payParams);

        // SOAP isteği ve yanıtını logla
        error_log("SOAP Request: " . $client->__getLastRequest());
        error_log("SOAP Response: " . $client->__getLastResponse());

        // Yanıt kontrolü
        if (!isset($payResponse->TP_WMD_PayResult)) {
            throw new Exception("TP_WMD_Pay yanıtı geçersiz.");
        }

        $payResult = $payResponse->TP_WMD_PayResult;

        // Sonuç kontrolü
        if (!isset($payResult->Sonuc) || $payResult->Sonuc <= 0) {
            $hataMesaji = isset($payResult->Sonuc_Ack) ? $payResult->Sonuc_Ack : "Bilinmeyen bir hata oluştu.";
            error_log("TP_WMD_Pay işlemi başarısız. Sonuç: " . $payResult->Sonuc . ", Hata Mesajı: " . $hataMesaji);
            throw new Exception("TP_WMD_Pay işlemi başarısız: " . $hataMesaji);
        }

        // İşlem başarılı, dekont ID'si ve diğer bilgileri yazdır
        echo "Ödeme başarıyla gerçekleştirildi!<br>";
        echo "İşlem ID: " . $payResult->Siparis_ID . "<br>";
        echo "Dekont ID: " . $payResult->Dekont_ID . "<br>";
        echo "Banka Transaction ID: " . $payResult->Bank_Trans_ID . "<br>";
        echo "Banka AuthCode: " . $payResult->Bank_AuthCode . "<br>";
        echo "Banka HostMsg: " . $payResult->Bank_HostMsg . "<br>";
        echo "Komisyon Oranı: " . $payResult->Komisyon_Oran . "%<br>";

    } catch (SoapFault $soapFault) {
        error_log("SOAP Hatası: " . $soapFault->getMessage());
        echo "Ödeme servisi ile iletişim sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.";
    } catch (Exception $e) {
        error_log("TP_WMD_Pay Hatası: " . $e->getMessage());
        echo "Ödeme işlemi sırasında bir hata oluştu: " . $e->getMessage();
    }
} else {
    echo "Gerekli 3D doğrulama bilgileri alınamadı.";
}
?>
